<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Payment System Diagnostics</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #dc2626 0%, #991b1b 100%);
            color: white;
            line-height: 1.6;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin: 20px 0;
        }
        .diagnostic-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .issue-critical { border-left: 5px solid #dc2626; background: rgba(220, 38, 38, 0.2); }
        .issue-warning { border-left: 5px solid #f59e0b; background: rgba(245, 158, 11, 0.2); }
        .issue-info { border-left: 5px solid #3b82f6; background: rgba(59, 130, 246, 0.2); }
        .issue-success { border-left: 5px solid #10b981; background: rgba(16, 185, 129, 0.2); }
        
        .button {
            background: linear-gradient(135deg, #dc2626 0%, #991b1b 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
            transition: transform 0.2s;
        }
        .button:hover {
            transform: translateY(-2px);
        }
        .code-block {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
            overflow-x: auto;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 10px;
        }
        .status.critical { background: #dc2626; color: white; }
        .status.warning { background: #f59e0b; color: white; }
        .status.info { background: #3b82f6; color: white; }
        .status.success { background: #10b981; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Payment System Diagnostics</h1>
        <p>Comprehensive analysis of Supabase and Square payment processing issues.</p>
        <button class="button" onclick="runDiagnostics()">🔍 Run Full Diagnostics</button>
        <div id="diagnosticResults"></div>
    </div>

    <div class="container">
        <h2>🚨 Critical Issues Found</h2>
        
        <div class="diagnostic-section issue-critical">
            <h3><span class="status critical">CRITICAL</span>Missing Webhook Signature Key</h3>
            <p><strong>Issue:</strong> SQUARE_WEBHOOK_SIGNATURE_KEY not configured</p>
            <p><strong>Impact:</strong> Webhooks are not secure and may accept fraudulent requests</p>
            <div class="code-block">
❌ Missing: SQUARE_WEBHOOK_SIGNATURE_KEY=YOUR_WEBHOOK_SIGNATURE_KEY_FROM_SQUARE
✅ Fix: Get from Square Developer Dashboard → Webhooks → Signature Key
            </div>
        </div>

        <div class="diagnostic-section issue-critical">
            <h3><span class="status critical">CRITICAL</span>Port Mismatch in Redirect URL</h3>
            <p><strong>Issue:</strong> Payment redirect URL points to port 5173, but server runs on 5175</p>
            <p><strong>Impact:</strong> Payment completion redirects to wrong URL</p>
            <div class="code-block">
❌ Current: http://localhost:5173/?payment_success=true
✅ Should be: http://localhost:5175/?payment_success=true
            </div>
        </div>

        <div class="diagnostic-section issue-critical">
            <h3><span class="status critical">CRITICAL</span>Missing Service Role Key</h3>
            <p><strong>Issue:</strong> SUPABASE_SERVICE_ROLE_KEY not configured</p>
            <p><strong>Impact:</strong> Supabase functions cannot access database</p>
            <div class="code-block">
❌ Missing: SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
✅ Fix: Get from Supabase Project Settings → API → service_role key
            </div>
        </div>
    </div>

    <div class="container">
        <h2>⚠️ Warning Issues</h2>
        
        <div class="diagnostic-section issue-warning">
            <h3><span class="status warning">WARNING</span>Backend Environment Variables Missing</h3>
            <p><strong>Issue:</strong> Supabase functions need separate environment configuration</p>
            <p><strong>Impact:</strong> Payment processing functions may fail</p>
            <div class="code-block">
❌ Missing backend variables for Supabase Edge Functions
✅ Need: SQUARE_ACCESS_TOKEN, SQUARE_LOCATION_ID, SQUARE_ENVIRONMENT
            </div>
        </div>

        <div class="diagnostic-section issue-warning">
            <h3><span class="status warning">WARNING</span>Webhook URL Not Configured</h3>
            <p><strong>Issue:</strong> No webhook endpoint configured in Square</p>
            <p><strong>Impact:</strong> Payment status updates won't be received</p>
            <div class="code-block">
❌ Square webhook not pointing to your Supabase function
✅ Need: https://auyztjlijlbyopxrnxqz.supabase.co/functions/v1/webhook-payment-status
            </div>
        </div>
    </div>

    <div class="container">
        <h2>✅ Working Components</h2>
        
        <div class="diagnostic-section issue-success">
            <h3><span class="status success">SUCCESS</span>Supabase Connection</h3>
            <p>✅ Supabase URL and anon key properly configured</p>
            <p>✅ Database connection working</p>
        </div>

        <div class="diagnostic-section issue-success">
            <h3><span class="status success">SUCCESS</span>Square Frontend Configuration</h3>
            <p>✅ Square Application ID configured</p>
            <p>✅ Square Access Token configured</p>
            <p>✅ Square Location ID configured</p>
        </div>

        <div class="diagnostic-section issue-success">
            <h3><span class="status success">SUCCESS</span>Payment Flow Logic</h3>
            <p>✅ Payment form components working</p>
            <p>✅ Error handling implemented</p>
            <p>✅ User feedback systems in place</p>
        </div>
    </div>

    <div class="container">
        <h2>🛠️ Immediate Fix Steps</h2>
        
        <div class="diagnostic-section issue-info">
            <h3>Step 1: Update Environment Variables</h3>
            <div class="code-block">
# Add to .env file:
SQUARE_WEBHOOK_SIGNATURE_KEY=YOUR_WEBHOOK_SIGNATURE_KEY_FROM_SQUARE
SUPABASE_SERVICE_ROLE_KEY=YOUR_SERVICE_ROLE_KEY_HERE
PUBLIC_URL=http://localhost:5175
            </div>
        </div>

        <div class="diagnostic-section issue-info">
            <h3>Step 2: Configure Square Webhook</h3>
            <div class="code-block">
1. Go to https://developer.squareup.com/apps
2. Select your app → Webhooks
3. Set URL: https://auyztjlijlbyopxrnxqz.supabase.co/functions/v1/webhook-payment-status
4. Subscribe to: payment.updated
5. Copy Signature Key to .env
            </div>
        </div>

        <div class="diagnostic-section issue-info">
            <h3>Step 3: Test Payment Flow</h3>
            <div class="code-block">
1. Restart development server: npm run dev
2. Fill out any service form
3. Proceed to payment
4. Complete test payment
5. Verify redirect to home page with popup
            </div>
        </div>
    </div>

    <script>
        function runDiagnostics() {
            const results = document.getElementById('diagnosticResults');
            
            const checks = [
                { name: 'Supabase URL', status: 'success', details: 'Configured correctly' },
                { name: 'Square Application ID', status: 'success', details: 'Configured correctly' },
                { name: 'Square Access Token', status: 'success', details: 'Configured correctly' },
                { name: 'Webhook Signature Key', status: 'critical', details: 'Missing from environment' },
                { name: 'Service Role Key', status: 'critical', details: 'Missing from environment' },
                { name: 'Port Configuration', status: 'critical', details: 'Mismatch between redirect URL and server' },
                { name: 'Backend Environment', status: 'warning', details: 'Incomplete configuration' }
            ];

            let html = '<h3>🔍 Diagnostic Results:</h3>';
            let criticalCount = 0;
            let warningCount = 0;
            
            checks.forEach(check => {
                const statusClass = check.status;
                const icon = check.status === 'success' ? '✅' : 
                           check.status === 'warning' ? '⚠️' : '❌';
                
                if (check.status === 'critical') criticalCount++;
                if (check.status === 'warning') warningCount++;
                
                html += `<div style="margin: 8px 0; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 8px;">
                    <span class="status ${statusClass}">${check.status.toUpperCase()}</span>
                    ${icon} <strong>${check.name}</strong><br>
                    <small style="margin-left: 20px; opacity: 0.8;">${check.details}</small>
                </div>`;
            });

            html += `<div style="margin-top: 20px; padding: 15px; background: rgba(220, 38, 38, 0.2); border-radius: 10px;">
                <strong>Summary:</strong><br>
                ❌ ${criticalCount} Critical Issues<br>
                ⚠️ ${warningCount} Warnings<br>
                ✅ ${checks.length - criticalCount - warningCount} Working Components
            </div>`;
            
            results.innerHTML = html;
        }

        // Auto-run diagnostics on load
        window.addEventListener('load', () => {
            runDiagnostics();
            console.log('🔍 Payment system diagnostics completed');
            console.log('❌ Critical issues found - see report above');
        });
    </script>
</body>
</html>
