import Node from "../core/Node.js";
import { ShaderNodeObject } from "../tsl/TSLCore.js";

export const positionGeometry: ShaderNodeObject<Node>;
export const positionLocal: ShaderNodeObject<Node>;
export const positionPrevious: ShaderNodeObject<Node>;
export const positionWorld: ShaderNodeObject<Node>;
export const positionWorldDirection: ShaderNodeObject<Node>;
export const positionView: ShaderNodeObject<Node>;
export const positionViewDirection: ShaderNodeObject<Node>;
