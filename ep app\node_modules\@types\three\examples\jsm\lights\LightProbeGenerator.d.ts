import { <PERSON>ubeTexture, LightProbe, WebGLCubeRenderTarget, WebG<PERSON>enderer } from "three";
import { WebGPURenderer } from "three/webgpu";

export namespace LightProbeGenerator {
    function fromCubeTexture(cubeTexture: CubeTexture): LightProbe;
    function fromCubeRenderTarget(
        renderer: WebG<PERSON>enderer | WebGPURenderer,
        cubeRenderTarget: WebGLCubeRenderTarget,
    ): Promise<LightProbe>;
}
