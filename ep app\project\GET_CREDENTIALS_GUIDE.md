# 🚀 Get Your Credentials - Complete Setup Guide

## 📋 What You Need
Your app needs credentials from two services:
1. **Supabase** (Database) - FREE
2. **Square** (Payments) - FREE for testing

## 🗄️ STEP 1: Supabase Setup (5 minutes)

### Create Account
1. Go to **https://supabase.com**
2. Click **"Start your project"**
3. Sign up with GitHub/Google (recommended) or email
4. ✅ **It's completely FREE for development**

### Create Project
1. Click **"New Project"**
2. **Organization**: Choose any name or create new
3. **Project Name**: `empire-pro-cleaning` (or your choice)
4. **Database Password**: Create strong password (SAVE THIS!)
5. **Region**: Choose closest to your location
6. Click **"Create new project"**
7. ⏳ **Wait 2-3 minutes** for project to initialize

### Get Your Credentials
Once project is ready:
1. Go to **Settings** → **API**
2. Copy these 3 values:

```env
# Project URL (starts with https://)
VITE_SUPABASE_URL=https://your-project-id.supabase.co

# anon public key (starts with eyJ)
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# service_role key (starts with eyJ) 
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## 💳 STEP 2: Square Setup (10 minutes)

### Create Developer Account
1. Go to **https://developer.squareup.com**
2. Click **"Get Started"**
3. Sign up (FREE - no business account needed for testing)
4. Complete basic profile

### Create Application
1. Go to **"My Apps"** or **"Applications"**
2. Click **"Create App"** or **"+"**
3. **App Name**: `Empire Pro Cleaning`
4. **Description**: `Cleaning service booking platform`
5. Click **"Create App"**

### Get Sandbox Credentials
In your new app dashboard:

1. **Application ID**: Copy from main dashboard
   ```
   sandbox-sq0idb-XXXXXXXXXX
   ```

2. **Access Token**: Look for "Sandbox Access Token"
   ```
   EAAAl-XXXXXXXXXX
   ```

3. **Location ID**: 
   - Go to **"Locations"** tab
   - Copy the Location ID (usually starts with letters)
   ```
   LXXXXXXXXX
   ```

### Setup Webhook (Important!)
1. Go to **"Webhooks"** tab
2. Click **"Create Webhook"** or **"+"**
3. **Webhook URL**: `https://your-supabase-project.supabase.co/functions/v1/webhook-payment-status`
4. **Events**: Select **"payment.updated"**
5. Click **"Create"**
6. **Copy the Signature Key** that appears:
   ```
   wbhk_XXXXXXXXXX
   ```

## 📝 STEP 3: Update Your .env File

Replace the placeholder values in your `.env` file:

```env
# ---------- Supabase ----------
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# ---------- Square (SANDBOX) ----------
VITE_SQUARE_APPLICATION_ID=sandbox-sq0idb-XXXXXXXXXX
VITE_SQUARE_LOCATION_ID=LXXXXXXXXX
VITE_SQUARE_ACCESS_TOKEN=EAAAl-XXXXXXXXXX
VITE_SQUARE_ENVIRONMENT=sandbox

# ---------- Backend Configuration ----------
SQUARE_ACCESS_TOKEN=EAAAl-XXXXXXXXXX
SQUARE_LOCATION_ID=LXXXXXXXXX
SQUARE_ENVIRONMENT=sandbox
SQUARE_WEBHOOK_SIGNATURE_KEY=wbhk_XXXXXXXXXX

SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# ---------- Public URL ----------
PUBLIC_URL=http://localhost:5175
```

## 🧪 STEP 4: Test Your Setup

### Restart Development Server
```bash
# Stop current server (Ctrl+C)
# Then restart:
npm run dev
```

### Test Database Connection
1. Open your app: `http://localhost:5175`
2. Try to sign up for an account
3. ✅ **Success**: Account created = Supabase working

### Test Payment Flow
1. Fill out any service form
2. Proceed to payment
3. ✅ **Success**: Redirected to Square = Payment setup working

## 🆘 Need Help?

### Common Issues:

**"Supabase not configured"**
- Check VITE_SUPABASE_URL starts with `https://`
- Check VITE_SUPABASE_ANON_KEY starts with `eyJ`
- Restart development server

**"Square not configured"**
- Check VITE_SQUARE_APPLICATION_ID starts with `sandbox-sq0idb-`
- Check VITE_SQUARE_ACCESS_TOKEN starts with `EAAAl`
- Make sure no extra spaces in .env file

**"Payment redirect fails"**
- Update webhook URL in Square dashboard
- Make sure PUBLIC_URL matches your dev server port

### Get Support:
1. Check browser console for specific errors
2. Verify all credentials are copied exactly
3. Make sure .env file has no quotes around values
4. Restart server after any .env changes

## 🎉 Success Indicators

Your setup is working when:
- ✅ App loads without "not configured" errors
- ✅ You can create user accounts
- ✅ Forms submit successfully
- ✅ Payment button redirects to Square
- ✅ Test payments complete successfully

## 💰 Cost Information

**Supabase**: 
- FREE tier: 500MB database, 2GB bandwidth
- Perfect for development and small businesses

**Square**:
- FREE sandbox testing (unlimited)
- Live payments: 2.9% + 30¢ per transaction
- No monthly fees

Both services are completely free for development and testing!
