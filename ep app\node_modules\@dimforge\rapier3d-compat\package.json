{"name": "@dimforge/rapier3d-compat", "collaborators": ["<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>"], "description": "3-dimensional physics engine in Rust - official JS bindings. Compatibility package with inlined webassembly as base64.", "version": "0.12.0", "license": "Apache-2.0", "repository": {"type": "git", "url": "https://github.com/dimforge/rapier.js"}, "files": ["*"], "module": "rapier.es.js", "homepage": "https://rapier.rs", "types": "rapier.d.ts", "sideEffects": ["./snippets/*"], "keywords": ["physics", "dynamics", "rigid", "real-time", "joints"], "main": "rapier.cjs.js"}